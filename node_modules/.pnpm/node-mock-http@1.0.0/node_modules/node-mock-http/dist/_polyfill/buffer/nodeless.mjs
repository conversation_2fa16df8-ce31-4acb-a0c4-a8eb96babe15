const w=[],y=[],rt=typeof Uint8Array>"u"?Array:Uint8Array,N="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(let t=0,e=N.length;t<e;++t)w[t]=N[t],y[N.charCodeAt(t)]=t;y[45]=62,y[95]=63;function ot(t){const e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");let n=t.indexOf("=");n===-1&&(n=e);const r=n===e?0:4-n%4;return[n,r]}function ft(t,e,n){return(e+n)*3/4-n}function it(t){let e;const n=ot(t),r=n[0],o=n[1],f=new rt(ft(t,r,o));let u=0;const h=o>0?r-4:r;let c;for(c=0;c<h;c+=4)e=y[t.charCodeAt(c)]<<18|y[t.charCodeAt(c+1)]<<12|y[t.charCodeAt(c+2)]<<6|y[t.charCodeAt(c+3)],f[u++]=e>>16&255,f[u++]=e>>8&255,f[u++]=e&255;return o===2&&(e=y[t.charCodeAt(c)]<<2|y[t.charCodeAt(c+1)]>>4,f[u++]=e&255),o===1&&(e=y[t.charCodeAt(c)]<<10|y[t.charCodeAt(c+1)]<<4|y[t.charCodeAt(c+2)]>>2,f[u++]=e>>8&255,f[u++]=e&255),f}function ut(t){return w[t>>18&63]+w[t>>12&63]+w[t>>6&63]+w[t&63]}function st(t,e,n){let r;const o=[];for(let f=e;f<n;f+=3)r=(t[f]<<16&16711680)+(t[f+1]<<8&65280)+(t[f+2]&255),o.push(ut(r));return o.join("")}function P(t){let e;const n=t.length,r=n%3,o=[],f=16383;for(let u=0,h=n-r;u<h;u+=f)o.push(st(t,u,u+f>h?h:u+f));return r===1?(e=t[n-1],o.push(w[e>>2]+w[e<<4&63]+"==")):r===2&&(e=(t[n-2]<<8)+t[n-1],o.push(w[e>>10]+w[e>>4&63]+w[e<<2&63]+"=")),o.join("")}/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */function T(t,e,n,r,o){let f,u;const h=o*8-r-1,c=(1<<h)-1,l=c>>1;let s=-7,a=n?o-1:0;const I=n?-1:1;let d=t[e+a];for(a+=I,f=d&(1<<-s)-1,d>>=-s,s+=h;s>0;)f=f*256+t[e+a],a+=I,s-=8;for(u=f&(1<<-s)-1,f>>=-s,s+=r;s>0;)u=u*256+t[e+a],a+=I,s-=8;if(f===0)f=1-l;else{if(f===c)return u?Number.NaN:(d?-1:1)*Number.POSITIVE_INFINITY;u=u+Math.pow(2,r),f=f-l}return(d?-1:1)*u*Math.pow(2,f-r)}function F(t,e,n,r,o,f){let u,h,c,l=f*8-o-1;const s=(1<<l)-1,a=s>>1,I=o===23?Math.pow(2,-24)-Math.pow(2,-77):0;let d=r?0:f-1;const L=r?1:-1,nt=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),Number.isNaN(e)||e===Number.POSITIVE_INFINITY?(h=Number.isNaN(e)?1:0,u=s):(u=Math.floor(Math.log2(e)),e*(c=Math.pow(2,-u))<1&&(u--,c*=2),e+=u+a>=1?I/c:I*Math.pow(2,1-a),e*c>=2&&(u++,c/=2),u+a>=s?(h=0,u=s):u+a>=1?(h=(e*c-1)*Math.pow(2,o),u=u+a):(h=e*Math.pow(2,a-1)*Math.pow(2,o),u=0));o>=8;)t[n+d]=h&255,d+=L,h/=256,o-=8;for(u=u<<o|h,l+=o;l>0;)t[n+d]=u&255,d+=L,u/=256,l-=8;t[n+d-L]|=nt*128}const k=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null,ht=50,S=2147483647;i.TYPED_ARRAY_SUPPORT=ct(),!i.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This environment lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function ct(){try{const t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),t.foo()===42}catch{return!1}}Object.defineProperty(i.prototype,"parent",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.buffer}}),Object.defineProperty(i.prototype,"offset",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.byteOffset}});function m(t){if(t>S)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,i.prototype),e}function i(t,e,n){if(typeof t=="number"){if(typeof e=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return _(t)}return j(t,e,n)}i.poolSize=8192;function j(t,e,n){if(typeof t=="string")return lt(t,e);if(ArrayBuffer.isView(t))return pt(t);if(t==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(b(t,ArrayBuffer)||t&&b(t.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(b(t,SharedArrayBuffer)||t&&b(t.buffer,SharedArrayBuffer)))return Y(t,e,n);if(typeof t=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');const r=t.valueOf&&t.valueOf();if(r!=null&&r!==t)return i.from(r,e,n);const o=gt(t);if(o)return o;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof t[Symbol.toPrimitive]=="function")return i.from(t[Symbol.toPrimitive]("string"),e,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}i.from=function(t,e,n){return j(t,e,n)},Object.setPrototypeOf(i.prototype,Uint8Array.prototype),Object.setPrototypeOf(i,Uint8Array);function D(t){if(typeof t!="number")throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function at(t,e,n){return D(t),t<=0?m(t):e!==void 0?typeof n=="string"?m(t).fill(e,n):m(t).fill(e):m(t)}i.alloc=function(t,e,n){return at(t,e,n)};function _(t){return D(t),m(t<0?0:x(t)|0)}i.allocUnsafe=function(t){return _(t)},i.allocUnsafeSlow=function(t){return _(t)};function lt(t,e){if((typeof e!="string"||e==="")&&(e="utf8"),!i.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const n=z(t,e)|0;let r=m(n);const o=r.write(t,e);return o!==n&&(r=r.slice(0,o)),r}function v(t){const e=t.length<0?0:x(t.length)|0,n=m(e);for(let r=0;r<e;r+=1)n[r]=t[r]&255;return n}function pt(t){if(b(t,Uint8Array)){const e=new Uint8Array(t);return Y(e.buffer,e.byteOffset,e.byteLength)}return v(t)}function Y(t,e,n){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw new RangeError('"length" is outside of buffer bounds');let r;return e===void 0&&n===void 0?r=new Uint8Array(t):n===void 0?r=new Uint8Array(t,e):r=new Uint8Array(t,e,n),Object.setPrototypeOf(r,i.prototype),r}function gt(t){if(i.isBuffer(t)){const e=x(t.length)|0,n=m(e);return n.length===0||t.copy(n,0,0,e),n}if(t.length!==void 0)return typeof t.length!="number"||M(t.length)?m(0):v(t);if(t.type==="Buffer"&&Array.isArray(t.data))return v(t.data)}function x(t){if(t>=S)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+S.toString(16)+" bytes");return t|0}i.isBuffer=function(t){return t!=null&&t._isBuffer===!0&&t!==i.prototype},i.compare=function(t,e){if(b(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),b(e,Uint8Array)&&(e=i.from(e,e.offset,e.byteLength)),!i.isBuffer(t)||!i.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,r=e.length;for(let o=0,f=Math.min(n,r);o<f;++o)if(t[o]!==e[o]){n=t[o],r=e[o];break}return n<r?-1:r<n?1:0},i.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(t.length===0)return i.alloc(0);let n;if(e===void 0)for(e=0,n=0;n<t.length;++n)e+=t[n].length;const r=i.allocUnsafe(e);let o=0;for(n=0;n<t.length;++n){let f=t[n];if(b(f,Uint8Array))o+f.length>r.length?(i.isBuffer(f)||(f=i.from(f.buffer,f.byteOffset,f.byteLength)),f.copy(r,o)):Uint8Array.prototype.set.call(r,f,o);else if(i.isBuffer(f))f.copy(r,o);else throw new TypeError('"list" argument must be an Array of Buffers');o+=f.length}return r};function z(t,e){if(i.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||b(t,ArrayBuffer))return t.byteLength;if(typeof t!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const n=t.length,r=arguments.length>2&&arguments[2]===!0;if(!r&&n===0)return 0;let o=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return $(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return n*2;case"hex":return n>>>1;case"base64":return et(t).length;default:{if(o)return r?-1:$(t).length;e=(""+e).toLowerCase(),o=!0}}}i.byteLength=z;function yt(t,e,n){let r=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((n===void 0||n>this.length)&&(n=this.length),n<=0)||(n>>>=0,e>>>=0,n<=e))return"";for(t||(t="utf8");;)switch(t){case"hex":return Rt(this,e,n);case"utf8":case"utf-8":return q(this,e,n);case"ascii":return At(this,e,n);case"latin1":case"binary":return Ut(this,e,n);case"base64":return Bt(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Tt(this,e,n);default:{if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}}i.prototype._isBuffer=!0;function B(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}i.prototype.swap16=function(){const t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)B(this,e,e+1);return this},i.prototype.swap32=function(){const t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)B(this,e,e+3),B(this,e+1,e+2);return this},i.prototype.swap64=function(){const t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)B(this,e,e+7),B(this,e+1,e+6),B(this,e+2,e+5),B(this,e+3,e+4);return this},i.prototype.toString=function(){const t=this.length;return t===0?"":arguments.length===0?q(this,0,t):Reflect.apply(yt,this,arguments)},i.prototype.toLocaleString=i.prototype.toString,i.prototype.equals=function(t){if(!i.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?!0:i.compare(this,t)===0},i.prototype.inspect=function(){let t="";const e=ht;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},k&&(i.prototype[k]=i.prototype.inspect),i.prototype.compare=function(t,e,n,r,o){if(b(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),!i.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(e===void 0&&(e=0),n===void 0&&(n=t?t.length:0),r===void 0&&(r=0),o===void 0&&(o=this.length),e<0||n>t.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&e>=n)return 0;if(r>=o)return-1;if(e>=n)return 1;if(e>>>=0,n>>>=0,r>>>=0,o>>>=0,this===t)return 0;let f=o-r,u=n-e;const h=Math.min(f,u),c=this.slice(r,o),l=t.slice(e,n);for(let s=0;s<h;++s)if(c[s]!==l[s]){f=c[s],u=l[s];break}return f<u?-1:u<f?1:0};function V(t,e,n,r,o){if(t.length===0)return-1;if(typeof n=="string"?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,M(n)&&(n=o?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(o)return-1;n=t.length-1}else if(n<0)if(o)n=0;else return-1;if(typeof e=="string"&&(e=i.from(e,r)),i.isBuffer(e))return e.length===0?-1:G(t,e,n,r,o);if(typeof e=="number")return e=e&255,typeof Uint8Array.prototype.indexOf=="function"?o?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):G(t,[e],n,r,o);throw new TypeError("val must be string, number or Buffer")}function G(t,e,n,r,o){let f=1,u=t.length,h=e.length;if(r!==void 0&&(r=String(r).toLowerCase(),r==="ucs2"||r==="ucs-2"||r==="utf16le"||r==="utf-16le")){if(t.length<2||e.length<2)return-1;f=2,u/=2,h/=2,n/=2}function c(s,a){return f===1?s[a]:s.readUInt16BE(a*f)}let l;if(o){let s=-1;for(l=n;l<u;l++)if(c(t,l)===c(e,s===-1?0:l-s)){if(s===-1&&(s=l),l-s+1===h)return s*f}else s!==-1&&(l-=l-s),s=-1}else for(n+h>u&&(n=u-h),l=n;l>=0;l--){let s=!0;for(let a=0;a<h;a++)if(c(t,l+a)!==c(e,a)){s=!1;break}if(s)return l}return-1}i.prototype.includes=function(t,e,n){return this.indexOf(t,e,n)!==-1},i.prototype.indexOf=function(t,e,n){return V(this,t,e,n,!0)},i.prototype.lastIndexOf=function(t,e,n){return V(this,t,e,n,!1)};function dt(t,e,n,r){n=Number(n)||0;const o=t.length-n;r?(r=Number(r),r>o&&(r=o)):r=o;const f=e.length;r>f/2&&(r=f/2);let u;for(u=0;u<r;++u){const h=Number.parseInt(e.slice(u*2,u*2+2),16);if(M(h))return u;t[n+u]=h}return u}function wt(t,e,n,r){return O($(e,t.length-n),t,n,r)}function bt(t,e,n,r){return O(St(e),t,n,r)}function mt(t,e,n,r){return O(et(e),t,n,r)}function Et(t,e,n,r){return O(_t(e,t.length-n),t,n,r)}i.prototype.write=function(t,e,n,r){if(e===void 0)r="utf8",n=this.length,e=0;else if(n===void 0&&typeof e=="string")r=e,n=this.length,e=0;else if(Number.isFinite(e))e=e>>>0,Number.isFinite(n)?(n=n>>>0,r===void 0&&(r="utf8")):(r=n,n=void 0);else throw new TypeError("Buffer.write(string, encoding, offset[, length]) is no longer supported");const o=this.length-e;if((n===void 0||n>o)&&(n=o),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let f=!1;for(;;)switch(r){case"hex":return dt(this,t,e,n);case"utf8":case"utf-8":return wt(this,t,e,n);case"ascii":case"latin1":case"binary":return bt(this,t,e,n);case"base64":return mt(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Et(this,t,e,n);default:{if(f)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),f=!0}}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function Bt(t,e,n){return e===0&&n===t.length?P(t):P(t.slice(e,n))}function q(t,e,n){n=Math.min(t.length,n);const r=[];let o=e;for(;o<n;){const f=t[o];let u=null,h=f>239?4:f>223?3:f>191?2:1;if(o+h<=n){let c,l,s,a;switch(h){case 1:{f<128&&(u=f);break}case 2:{c=t[o+1],(c&192)===128&&(a=(f&31)<<6|c&63,a>127&&(u=a));break}case 3:{c=t[o+1],l=t[o+2],(c&192)===128&&(l&192)===128&&(a=(f&15)<<12|(c&63)<<6|l&63,a>2047&&(a<55296||a>57343)&&(u=a));break}case 4:c=t[o+1],l=t[o+2],s=t[o+3],(c&192)===128&&(l&192)===128&&(s&192)===128&&(a=(f&15)<<18|(c&63)<<12|(l&63)<<6|s&63,a>65535&&a<1114112&&(u=a))}}u===null?(u=65533,h=1):u>65535&&(u-=65536,r.push(u>>>10&1023|55296),u=56320|u&1023),r.push(u),o+=h}return It(r)}const W=4096;function It(t){const e=t.length;if(e<=W)return String.fromCharCode.apply(String,t);let n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=W));return n}function At(t,e,n){let r="";n=Math.min(t.length,n);for(let o=e;o<n;++o)r+=String.fromCharCode(t[o]&127);return r}function Ut(t,e,n){let r="";n=Math.min(t.length,n);for(let o=e;o<n;++o)r+=String.fromCharCode(t[o]);return r}function Rt(t,e,n){const r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);let o="";for(let f=e;f<n;++f)o+=vt[t[f]];return o}function Tt(t,e,n){const r=t.slice(e,n);let o="";for(let f=0;f<r.length-1;f+=2)o+=String.fromCharCode(r[f]+r[f+1]*256);return o}i.prototype.slice=function(t,e){const n=this.length;t=Math.trunc(t),e=e===void 0?n:Math.trunc(e),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),e<t&&(e=t);const r=this.subarray(t,e);return Object.setPrototypeOf(r,i.prototype),r};function p(t,e,n){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}i.prototype.readUintLE=i.prototype.readUIntLE=function(t,e,n){t=t>>>0,e=e>>>0,n||p(t,e,this.length);let r=this[t],o=1,f=0;for(;++f<e&&(o*=256);)r+=this[t+f]*o;return r},i.prototype.readUintBE=i.prototype.readUIntBE=function(t,e,n){t=t>>>0,e=e>>>0,n||p(t,e,this.length);let r=this[t+--e],o=1;for(;e>0&&(o*=256);)r+=this[t+--e]*o;return r},i.prototype.readUint8=i.prototype.readUInt8=function(t,e){return t=t>>>0,e||p(t,1,this.length),this[t]},i.prototype.readUint16LE=i.prototype.readUInt16LE=function(t,e){return t=t>>>0,e||p(t,2,this.length),this[t]|this[t+1]<<8},i.prototype.readUint16BE=i.prototype.readUInt16BE=function(t,e){return t=t>>>0,e||p(t,2,this.length),this[t]<<8|this[t+1]},i.prototype.readUint32LE=i.prototype.readUInt32LE=function(t,e){return t=t>>>0,e||p(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216},i.prototype.readUint32BE=i.prototype.readUInt32BE=function(t,e){return t=t>>>0,e||p(t,4,this.length),this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])},i.prototype.readBigUInt64LE=E(function(t){t=t>>>0,U(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&R(t,this.length-8);const r=e+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24,o=this[++t]+this[++t]*2**8+this[++t]*2**16+n*2**24;return BigInt(r)+(BigInt(o)<<BigInt(32))}),i.prototype.readBigUInt64BE=E(function(t){t=t>>>0,U(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&R(t,this.length-8);const r=e*2**24+this[++t]*2**16+this[++t]*2**8+this[++t],o=this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+n;return(BigInt(r)<<BigInt(32))+BigInt(o)}),i.prototype.readIntLE=function(t,e,n){t=t>>>0,e=e>>>0,n||p(t,e,this.length);let r=this[t],o=1,f=0;for(;++f<e&&(o*=256);)r+=this[t+f]*o;return o*=128,r>=o&&(r-=Math.pow(2,8*e)),r},i.prototype.readIntBE=function(t,e,n){t=t>>>0,e=e>>>0,n||p(t,e,this.length);let r=e,o=1,f=this[t+--r];for(;r>0&&(o*=256);)f+=this[t+--r]*o;return o*=128,f>=o&&(f-=Math.pow(2,8*e)),f},i.prototype.readInt8=function(t,e){return t=t>>>0,e||p(t,1,this.length),this[t]&128?(255-this[t]+1)*-1:this[t]},i.prototype.readInt16LE=function(t,e){t=t>>>0,e||p(t,2,this.length);const n=this[t]|this[t+1]<<8;return n&32768?n|4294901760:n},i.prototype.readInt16BE=function(t,e){t=t>>>0,e||p(t,2,this.length);const n=this[t+1]|this[t]<<8;return n&32768?n|4294901760:n},i.prototype.readInt32LE=function(t,e){return t=t>>>0,e||p(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},i.prototype.readInt32BE=function(t,e){return t=t>>>0,e||p(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},i.prototype.readBigInt64LE=E(function(t){t=t>>>0,U(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&R(t,this.length-8);const r=this[t+4]+this[t+5]*2**8+this[t+6]*2**16+(n<<24);return(BigInt(r)<<BigInt(32))+BigInt(e+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24)}),i.prototype.readBigInt64BE=E(function(t){t=t>>>0,U(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&R(t,this.length-8);const r=(e<<24)+this[++t]*2**16+this[++t]*2**8+this[++t];return(BigInt(r)<<BigInt(32))+BigInt(this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+n)}),i.prototype.readFloatLE=function(t,e){return t=t>>>0,e||p(t,4,this.length),T(this,t,!0,23,4)},i.prototype.readFloatBE=function(t,e){return t=t>>>0,e||p(t,4,this.length),T(this,t,!1,23,4)},i.prototype.readDoubleLE=function(t,e){return t=t>>>0,e||p(t,8,this.length),T(this,t,!0,52,8)},i.prototype.readDoubleBE=function(t,e){return t=t>>>0,e||p(t,8,this.length),T(this,t,!1,52,8)};function g(t,e,n,r,o,f){if(!i.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<f)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}i.prototype.writeUintLE=i.prototype.writeUIntLE=function(t,e,n,r){if(t=+t,e=e>>>0,n=n>>>0,!r){const u=Math.pow(2,8*n)-1;g(this,t,e,n,u,0)}let o=1,f=0;for(this[e]=t&255;++f<n&&(o*=256);)this[e+f]=t/o&255;return e+n},i.prototype.writeUintBE=i.prototype.writeUIntBE=function(t,e,n,r){if(t=+t,e=e>>>0,n=n>>>0,!r){const u=Math.pow(2,8*n)-1;g(this,t,e,n,u,0)}let o=n-1,f=1;for(this[e+o]=t&255;--o>=0&&(f*=256);)this[e+o]=t/f&255;return e+n},i.prototype.writeUint8=i.prototype.writeUInt8=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,1,255,0),this[e]=t&255,e+1},i.prototype.writeUint16LE=i.prototype.writeUInt16LE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,2,65535,0),this[e]=t&255,this[e+1]=t>>>8,e+2},i.prototype.writeUint16BE=i.prototype.writeUInt16BE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=t&255,e+2},i.prototype.writeUint32LE=i.prototype.writeUInt32LE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=t&255,e+4},i.prototype.writeUint32BE=i.prototype.writeUInt32BE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4};function J(t,e,n,r,o){tt(e,r,o,t,n,7);let f=Number(e&BigInt(4294967295));t[n++]=f,f=f>>8,t[n++]=f,f=f>>8,t[n++]=f,f=f>>8,t[n++]=f;let u=Number(e>>BigInt(32)&BigInt(4294967295));return t[n++]=u,u=u>>8,t[n++]=u,u=u>>8,t[n++]=u,u=u>>8,t[n++]=u,n}function X(t,e,n,r,o){tt(e,r,o,t,n,7);let f=Number(e&BigInt(4294967295));t[n+7]=f,f=f>>8,t[n+6]=f,f=f>>8,t[n+5]=f,f=f>>8,t[n+4]=f;let u=Number(e>>BigInt(32)&BigInt(4294967295));return t[n+3]=u,u=u>>8,t[n+2]=u,u=u>>8,t[n+1]=u,u=u>>8,t[n]=u,n+8}i.prototype.writeBigUInt64LE=E(function(t,e=0){return J(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),i.prototype.writeBigUInt64BE=E(function(t,e=0){return X(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),i.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e=e>>>0,!r){const h=Math.pow(2,8*n-1);g(this,t,e,n,h-1,-h)}let o=0,f=1,u=0;for(this[e]=t&255;++o<n&&(f*=256);)t<0&&u===0&&this[e+o-1]!==0&&(u=1),this[e+o]=Math.trunc(t/f)-u&255;return e+n},i.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e=e>>>0,!r){const h=Math.pow(2,8*n-1);g(this,t,e,n,h-1,-h)}let o=n-1,f=1,u=0;for(this[e+o]=t&255;--o>=0&&(f*=256);)t<0&&u===0&&this[e+o+1]!==0&&(u=1),this[e+o]=Math.trunc(t/f)-u&255;return e+n},i.prototype.writeInt8=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=t&255,e+1},i.prototype.writeInt16LE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,2,32767,-32768),this[e]=t&255,this[e+1]=t>>>8,e+2},i.prototype.writeInt16BE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=t&255,e+2},i.prototype.writeInt32LE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,4,2147483647,-2147483648),this[e]=t&255,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},i.prototype.writeInt32BE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4},i.prototype.writeBigInt64LE=E(function(t,e=0){return J(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),i.prototype.writeBigInt64BE=E(function(t,e=0){return X(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function H(t,e,n,r,o,f){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function K(t,e,n,r,o){return e=+e,n=n>>>0,o||H(t,e,n,4),F(t,e,n,r,23,4),n+4}i.prototype.writeFloatLE=function(t,e,n){return K(this,t,e,!0,n)},i.prototype.writeFloatBE=function(t,e,n){return K(this,t,e,!1,n)};function Q(t,e,n,r,o){return e=+e,n=n>>>0,o||H(t,e,n,8),F(t,e,n,r,52,8),n+8}i.prototype.writeDoubleLE=function(t,e,n){return Q(this,t,e,!0,n)},i.prototype.writeDoubleBE=function(t,e,n){return Q(this,t,e,!1,n)},i.prototype.copy=function(t,e,n,r){if(!i.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),!r&&r!==0&&(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n||t.length===0||this.length===0)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);const o=r-n;return this===t&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(e,n,r):Uint8Array.prototype.set.call(t,this.subarray(n,r),e),o},i.prototype.fill=function(t,e,n,r){if(typeof t=="string"){if(typeof e=="string"?(r=e,e=0,n=this.length):typeof n=="string"&&(r=n,n=this.length),r!==void 0&&typeof r!="string")throw new TypeError("encoding must be a string");if(typeof r=="string"&&!i.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(t.length===1){const f=t.charCodeAt(0);(r==="utf8"&&f<128||r==="latin1")&&(t=f)}}else typeof t=="number"?t=t&255:typeof t=="boolean"&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;e=e>>>0,n=n===void 0?this.length:n>>>0,t||(t=0);let o;if(typeof t=="number")for(o=e;o<n;++o)this[o]=t;else{const f=i.isBuffer(t)?t:i.from(t,r),u=f.length;if(u===0)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<n-e;++o)this[o+e]=f[o%u]}return this};const A={};function C(t,e,n){A[t]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:Reflect.apply(e,this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(r){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:r,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}C("ERR_BUFFER_OUT_OF_BOUNDS",function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),C("ERR_INVALID_ARG_TYPE",function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`},TypeError),C("ERR_OUT_OF_RANGE",function(t,e,n){let r=`The value of "${t}" is out of range.`,o=n;return Number.isInteger(n)&&Math.abs(n)>2**32?o=Z(String(n)):typeof n=="bigint"&&(o=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(o=Z(o)),o+="n"),r+=` It must be ${e}. Received ${o}`,r},RangeError);function Z(t){let e="",n=t.length;const r=t[0]==="-"?1:0;for(;n>=r+4;n-=3)e=`_${t.slice(n-3,n)}${e}`;return`${t.slice(0,n)}${e}`}function Ot(t,e,n){U(e,"offset"),(t[e]===void 0||t[e+n]===void 0)&&R(e,t.length-(n+1))}function tt(t,e,n,r,o,f){if(t>n||t<e){const u=typeof e=="bigint"?"n":"";let h;throw h=e===0||e===BigInt(0)?`>= 0${u} and < 2${u} ** ${(f+1)*8}${u}`:`>= -(2${u} ** ${(f+1)*8-1}${u}) and < 2 ** ${(f+1)*8-1}${u}`,new A.ERR_OUT_OF_RANGE("value",h,t)}Ot(r,o,f)}function U(t,e){if(typeof t!="number")throw new A.ERR_INVALID_ARG_TYPE(e,"number",t)}function R(t,e,n){throw Math.floor(t)!==t?(U(t,n),new A.ERR_OUT_OF_RANGE("offset","an integer",t)):e<0?new A.ERR_BUFFER_OUT_OF_BOUNDS:new A.ERR_OUT_OF_RANGE("offset",`>= 0 and <= ${e}`,t)}const Lt=/[^\w+/-]/g;function Nt(t){if(t=t.split("=")[0],t=t.trim().replace(Lt,""),t.length<2)return"";for(;t.length%4!==0;)t=t+"=";return t}function $(t,e){e=e||Number.POSITIVE_INFINITY;let n;const r=t.length;let o=null;const f=[];for(let u=0;u<r;++u){if(n=t.charCodeAt(u),n>55295&&n<57344){if(!o){if(n>56319){(e-=3)>-1&&f.push(239,191,189);continue}else if(u+1===r){(e-=3)>-1&&f.push(239,191,189);continue}o=n;continue}if(n<56320){(e-=3)>-1&&f.push(239,191,189),o=n;continue}n=(o-55296<<10|n-56320)+65536}else o&&(e-=3)>-1&&f.push(239,191,189);if(o=null,n<128){if((e-=1)<0)break;f.push(n)}else if(n<2048){if((e-=2)<0)break;f.push(n>>6|192,n&63|128)}else if(n<65536){if((e-=3)<0)break;f.push(n>>12|224,n>>6&63|128,n&63|128)}else if(n<1114112){if((e-=4)<0)break;f.push(n>>18|240,n>>12&63|128,n>>6&63|128,n&63|128)}else throw new Error("Invalid code point")}return f}function St(t){const e=[];for(let n=0;n<t.length;++n)e.push(t.charCodeAt(n)&255);return e}function _t(t,e){let n,r,o;const f=[];for(let u=0;u<t.length&&!((e-=2)<0);++u)n=t.charCodeAt(u),r=n>>8,o=n%256,f.push(o,r);return f}function et(t){return it(Nt(t))}function O(t,e,n,r){let o;for(o=0;o<r&&!(o+n>=e.length||o>=t.length);++o)e[o+n]=t[o];return o}function b(t,e){return t instanceof e||t!=null&&t.constructor!=null&&t.constructor.name!=null&&t.constructor.name===e.name}function M(t){return t!==t}const vt=function(){const t="0123456789abcdef",e=Array.from({length:256});for(let n=0;n<16;++n){const r=n*16;for(let o=0;o<16;++o)e[r+o]=t[n]+t[o]}return e}();function E(t){return typeof BigInt>"u"?xt:t}function xt(){throw new Error("BigInt not supported")}export{i as Buffer};
