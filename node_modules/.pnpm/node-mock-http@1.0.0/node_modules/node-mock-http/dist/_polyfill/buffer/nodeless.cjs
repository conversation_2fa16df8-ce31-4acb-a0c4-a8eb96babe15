"use strict";const a$1=[],l=[],x$1=typeof Uint8Array>"u"?Array:Uint8Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(let t=0,e=i.length;t<e;++t)a$1[t]=i[t],l[i.charCodeAt(t)]=t;l[45]=62,l[95]=63;function d$1(t){const e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");let n=t.indexOf("=");n===-1&&(n=e);const r=n===e?0:4-n%4;return[n,r]}function p(t,e,n){return(e+n)*3/4-n}function toByteArray(t){let e;const n=d$1(t),r=n[0],o=n[1],f=new x$1(p(t,r,o));let h=0;const E=o>0?r-4:r;let U;for(U=0;U<E;U+=4)e=l[t.charCodeAt(U)]<<18|l[t.charCodeAt(U+1)]<<12|l[t.charCodeAt(U+2)]<<6|l[t.charCodeAt(U+3)],f[h++]=e>>16&255,f[h++]=e>>8&255,f[h++]=e&255;return o===2&&(e=l[t.charCodeAt(U)]<<2|l[t.charCodeAt(U+1)]>>4,f[h++]=e&255),o===1&&(e=l[t.charCodeAt(U)]<<10|l[t.charCodeAt(U+1)]<<4|l[t.charCodeAt(U+2)]>>2,f[h++]=e>>8&255,f[h++]=e&255),f}function u(t){return a$1[t>>18&63]+a$1[t>>12&63]+a$1[t>>6&63]+a$1[t&63]}function A$1(t,e,n){let r;const o=[];for(let f=e;f<n;f+=3)r=(t[f]<<16&16711680)+(t[f+1]<<8&65280)+(t[f+2]&255),o.push(u(r));return o.join("")}function fromByteArray(t){let e;const n=t.length,r=n%3,o=[],f=16383;for(let h=0,E=n-r;h<E;h+=f)o.push(A$1(t,h,h+f>E?E:h+f));return r===1?(e=t[n-1],o.push(a$1[e>>2]+a$1[e<<4&63]+"==")):r===2&&(e=(t[n-2]<<8)+t[n-1],o.push(a$1[e>>10]+a$1[e>>4&63]+a$1[e<<2&63]+"=")),o.join("")}/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */function read(t,e,n,r,o){let f,h;const E=o*8-r-1,U=(1<<E)-1,et=U>>1;let c=-7,tt=n?o-1:0;const rt=n?-1:1;let nt=t[e+tt];for(tt+=rt,f=nt&(1<<-c)-1,nt>>=-c,c+=E;c>0;)f=f*256+t[e+tt],tt+=rt,c-=8;for(h=f&(1<<-c)-1,f>>=-c,c+=r;c>0;)h=h*256+t[e+tt],tt+=rt,c-=8;if(f===0)f=1-et;else{if(f===U)return h?Number.NaN:(nt?-1:1)*Number.POSITIVE_INFINITY;h=h+Math.pow(2,r),f=f-et}return(nt?-1:1)*h*Math.pow(2,f-r)}function write(t,e,n,r,o,f){let h,E,U,et=f*8-o-1;const c=(1<<et)-1,tt=c>>1,rt=o===23?Math.pow(2,-24)-Math.pow(2,-77):0;let nt=r?0:f-1;const ot=r?1:-1,ft=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),Number.isNaN(e)||e===Number.POSITIVE_INFINITY?(E=Number.isNaN(e)?1:0,h=c):(h=Math.floor(Math.log2(e)),e*(U=Math.pow(2,-h))<1&&(h--,U*=2),e+=h+tt>=1?rt/U:rt*Math.pow(2,1-tt),e*U>=2&&(h++,U/=2),h+tt>=c?(E=0,h=c):h+tt>=1?(E=(e*U-1)*Math.pow(2,o),h=h+tt):(E=e*Math.pow(2,tt-1)*Math.pow(2,o),h=0));o>=8;)t[n+nt]=E&255,nt+=ot,E/=256,o-=8;for(h=h<<o|E,et+=o;et>0;)t[n+nt]=h&255,nt+=ot,h/=256,et-=8;t[n+nt-ot]|=ft*128}const $=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null,INSPECT_MAX_BYTES=50,I=2147483647;Buffer.TYPED_ARRAY_SUPPORT=J(),!Buffer.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This environment lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function J(){try{const t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),t.foo()===42}catch{return!1}}Object.defineProperty(Buffer.prototype,"parent",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.buffer}}),Object.defineProperty(Buffer.prototype,"offset",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.byteOffset}});function w(t){if(t>I)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,Buffer.prototype),e}function Buffer(t,e,n){if(typeof t=="number"){if(typeof e=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return A(t)}return k(t,e,n)}Buffer.poolSize=8192;function k(t,e,n){if(typeof t=="string")return K(t,e);if(ArrayBuffer.isView(t))return Q(t);if(t==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(a(t,ArrayBuffer)||t&&a(t.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(a(t,SharedArrayBuffer)||t&&a(t.buffer,SharedArrayBuffer)))return T(t,e,n);if(typeof t=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');const r=t.valueOf&&t.valueOf();if(r!=null&&r!==t)return Buffer.from(r,e,n);const o=Z(t);if(o)return o;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof t[Symbol.toPrimitive]=="function")return Buffer.from(t[Symbol.toPrimitive]("string"),e,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}Buffer.from=function(t,e,n){return k(t,e,n)},Object.setPrototypeOf(Buffer.prototype,Uint8Array.prototype),Object.setPrototypeOf(Buffer,Uint8Array);function C(t){if(typeof t!="number")throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function z(t,e,n){return C(t),t<=0?w(t):e!==void 0?typeof n=="string"?w(t).fill(e,n):w(t).fill(e):w(t)}Buffer.alloc=function(t,e,n){return z(t,e,n)};function A(t){return C(t),w(t<0?0:b(t)|0)}Buffer.allocUnsafe=function(t){return A(t)},Buffer.allocUnsafeSlow=function(t){return A(t)};function K(t,e){if((typeof e!="string"||e==="")&&(e="utf8"),!Buffer.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const n=P(t,e)|0;let r=w(n);const o=r.write(t,e);return o!==n&&(r=r.slice(0,o)),r}function R(t){const e=t.length<0?0:b(t.length)|0,n=w(e);for(let r=0;r<e;r+=1)n[r]=t[r]&255;return n}function Q(t){if(a(t,Uint8Array)){const e=new Uint8Array(t);return T(e.buffer,e.byteOffset,e.byteLength)}return R(t)}function T(t,e,n){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw new RangeError('"length" is outside of buffer bounds');let r;return e===void 0&&n===void 0?r=new Uint8Array(t):n===void 0?r=new Uint8Array(t,e):r=new Uint8Array(t,e,n),Object.setPrototypeOf(r,Buffer.prototype),r}function Z(t){if(Buffer.isBuffer(t)){const e=b(t.length)|0,n=w(e);return n.length===0||t.copy(n,0,0,e),n}if(t.length!==void 0)return typeof t.length!="number"||N(t.length)?w(0):R(t);if(t.type==="Buffer"&&Array.isArray(t.data))return R(t.data)}function b(t){if(t>=I)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+I.toString(16)+" bytes");return t|0}Buffer.isBuffer=function(t){return t!=null&&t._isBuffer===!0&&t!==Buffer.prototype},Buffer.compare=function(t,e){if(a(t,Uint8Array)&&(t=Buffer.from(t,t.offset,t.byteLength)),a(e,Uint8Array)&&(e=Buffer.from(e,e.offset,e.byteLength)),!Buffer.isBuffer(t)||!Buffer.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,r=e.length;for(let o=0,f=Math.min(n,r);o<f;++o)if(t[o]!==e[o]){n=t[o],r=e[o];break}return n<r?-1:r<n?1:0},Buffer.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Buffer.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(t.length===0)return Buffer.alloc(0);let n;if(e===void 0)for(e=0,n=0;n<t.length;++n)e+=t[n].length;const r=Buffer.allocUnsafe(e);let o=0;for(n=0;n<t.length;++n){let f=t[n];if(a(f,Uint8Array))o+f.length>r.length?(Buffer.isBuffer(f)||(f=Buffer.from(f.buffer,f.byteOffset,f.byteLength)),f.copy(r,o)):Uint8Array.prototype.set.call(r,f,o);else if(Buffer.isBuffer(f))f.copy(r,o);else throw new TypeError('"list" argument must be an Array of Buffers');o+=f.length}return r};function P(t,e){if(Buffer.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||a(t,ArrayBuffer))return t.byteLength;if(typeof t!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const n=t.length,r=arguments.length>2&&arguments[2]===!0;if(!r&&n===0)return 0;let o=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return L(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return n*2;case"hex":return n>>>1;case"base64":return H(t).length;default:{if(o)return r?-1:L(t).length;e=(""+e).toLowerCase(),o=!0}}}Buffer.byteLength=P;function v(t,e,n){let r=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((n===void 0||n>this.length)&&(n=this.length),n<=0)||(n>>>=0,e>>>=0,n<=e))return"";for(t||(t="utf8");;)switch(t){case"hex":return cr(this,e,n);case"utf8":case"utf-8":return O(this,e,n);case"ascii":return fr(this,e,n);case"latin1":case"binary":return hr(this,e,n);case"base64":return or(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return pr(this,e,n);default:{if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}}Buffer.prototype._isBuffer=!0;function B(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}Buffer.prototype.swap16=function(){const t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)B(this,e,e+1);return this},Buffer.prototype.swap32=function(){const t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)B(this,e,e+3),B(this,e+1,e+2);return this},Buffer.prototype.swap64=function(){const t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)B(this,e,e+7),B(this,e+1,e+6),B(this,e+2,e+5),B(this,e+3,e+4);return this},Buffer.prototype.toString=function(){const t=this.length;return t===0?"":arguments.length===0?O(this,0,t):Reflect.apply(v,this,arguments)},Buffer.prototype.toLocaleString=Buffer.prototype.toString,Buffer.prototype.equals=function(t){if(!Buffer.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?!0:Buffer.compare(this,t)===0},Buffer.prototype.inspect=function(){let t="";const e=INSPECT_MAX_BYTES;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},$&&(Buffer.prototype[$]=Buffer.prototype.inspect),Buffer.prototype.compare=function(t,e,n,r,o){if(a(t,Uint8Array)&&(t=Buffer.from(t,t.offset,t.byteLength)),!Buffer.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(e===void 0&&(e=0),n===void 0&&(n=t?t.length:0),r===void 0&&(r=0),o===void 0&&(o=this.length),e<0||n>t.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&e>=n)return 0;if(r>=o)return-1;if(e>=n)return 1;if(e>>>=0,n>>>=0,r>>>=0,o>>>=0,this===t)return 0;let f=o-r,h=n-e;const E=Math.min(f,h),U=this.slice(r,o),et=t.slice(e,n);for(let c=0;c<E;++c)if(U[c]!==et[c]){f=U[c],h=et[c];break}return f<h?-1:h<f?1:0};function M(t,e,n,r,o){if(t.length===0)return-1;if(typeof n=="string"?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,N(n)&&(n=o?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(o)return-1;n=t.length-1}else if(n<0)if(o)n=0;else return-1;if(typeof e=="string"&&(e=Buffer.from(e,r)),Buffer.isBuffer(e))return e.length===0?-1:F(t,e,n,r,o);if(typeof e=="number")return e=e&255,typeof Uint8Array.prototype.indexOf=="function"?o?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):F(t,[e],n,r,o);throw new TypeError("val must be string, number or Buffer")}function F(t,e,n,r,o){let f=1,h=t.length,E=e.length;if(r!==void 0&&(r=String(r).toLowerCase(),r==="ucs2"||r==="ucs-2"||r==="utf16le"||r==="utf-16le")){if(t.length<2||e.length<2)return-1;f=2,h/=2,E/=2,n/=2}function U(c,tt){return f===1?c[tt]:c.readUInt16BE(tt*f)}let et;if(o){let c=-1;for(et=n;et<h;et++)if(U(t,et)===U(e,c===-1?0:et-c)){if(c===-1&&(c=et),et-c+1===E)return c*f}else c!==-1&&(et-=et-c),c=-1}else for(n+E>h&&(n=h-E),et=n;et>=0;et--){let c=!0;for(let tt=0;tt<E;tt++)if(U(t,et+tt)!==U(e,tt)){c=!1;break}if(c)return et}return-1}Buffer.prototype.includes=function(t,e,n){return this.indexOf(t,e,n)!==-1},Buffer.prototype.indexOf=function(t,e,n){return M(this,t,e,n,!0)},Buffer.prototype.lastIndexOf=function(t,e,n){return M(this,t,e,n,!1)};function rr(t,e,n,r){n=Number(n)||0;const o=t.length-n;r?(r=Number(r),r>o&&(r=o)):r=o;const f=e.length;r>f/2&&(r=f/2);let h;for(h=0;h<r;++h){const E=Number.parseInt(e.slice(h*2,h*2+2),16);if(N(E))return h;t[n+h]=E}return h}function tr(t,e,n,r){return d(L(e,t.length-n),t,n,r)}function ir(t,e,n,r){return d(ar(e),t,n,r)}function nr(t,e,n,r){return d(H(e),t,n,r)}function er(t,e,n,r){return d(wr(e,t.length-n),t,n,r)}Buffer.prototype.write=function(t,e,n,r){if(e===void 0)r="utf8",n=this.length,e=0;else if(n===void 0&&typeof e=="string")r=e,n=this.length,e=0;else if(Number.isFinite(e))e=e>>>0,Number.isFinite(n)?(n=n>>>0,r===void 0&&(r="utf8")):(r=n,n=void 0);else throw new TypeError("Buffer.write(string, encoding, offset[, length]) is no longer supported");const o=this.length-e;if((n===void 0||n>o)&&(n=o),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let f=!1;for(;;)switch(r){case"hex":return rr(this,t,e,n);case"utf8":case"utf-8":return tr(this,t,e,n);case"ascii":case"latin1":case"binary":return ir(this,t,e,n);case"base64":return nr(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return er(this,t,e,n);default:{if(f)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),f=!0}}},Buffer.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function or(t,e,n){return e===0&&n===t.length?fromByteArray(t):fromByteArray(t.slice(e,n))}function O(t,e,n){n=Math.min(t.length,n);const r=[];let o=e;for(;o<n;){const f=t[o];let h=null,E=f>239?4:f>223?3:f>191?2:1;if(o+E<=n){let U,et,c,tt;switch(E){case 1:{f<128&&(h=f);break}case 2:{U=t[o+1],(U&192)===128&&(tt=(f&31)<<6|U&63,tt>127&&(h=tt));break}case 3:{U=t[o+1],et=t[o+2],(U&192)===128&&(et&192)===128&&(tt=(f&15)<<12|(U&63)<<6|et&63,tt>2047&&(tt<55296||tt>57343)&&(h=tt));break}case 4:U=t[o+1],et=t[o+2],c=t[o+3],(U&192)===128&&(et&192)===128&&(c&192)===128&&(tt=(f&15)<<18|(U&63)<<12|(et&63)<<6|c&63,tt>65535&&tt<1114112&&(h=tt))}}h===null?(h=65533,E=1):h>65535&&(h-=65536,r.push(h>>>10&1023|55296),h=56320|h&1023),r.push(h),o+=E}return ur(r)}const D=4096;function ur(t){const e=t.length;if(e<=D)return String.fromCharCode.apply(String,t);let n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=D));return n}function fr(t,e,n){let r="";n=Math.min(t.length,n);for(let o=e;o<n;++o)r+=String.fromCharCode(t[o]&127);return r}function hr(t,e,n){let r="";n=Math.min(t.length,n);for(let o=e;o<n;++o)r+=String.fromCharCode(t[o]);return r}function cr(t,e,n){const r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);let o="";for(let f=e;f<n;++f)o+=xr[t[f]];return o}function pr(t,e,n){const r=t.slice(e,n);let o="";for(let f=0;f<r.length-1;f+=2)o+=String.fromCharCode(r[f]+r[f+1]*256);return o}Buffer.prototype.slice=function(t,e){const n=this.length;t=Math.trunc(t),e=e===void 0?n:Math.trunc(e),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),e<t&&(e=t);const r=this.subarray(t,e);return Object.setPrototypeOf(r,Buffer.prototype),r};function s(t,e,n){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}Buffer.prototype.readUintLE=Buffer.prototype.readUIntLE=function(t,e,n){t=t>>>0,e=e>>>0,n||s(t,e,this.length);let r=this[t],o=1,f=0;for(;++f<e&&(o*=256);)r+=this[t+f]*o;return r},Buffer.prototype.readUintBE=Buffer.prototype.readUIntBE=function(t,e,n){t=t>>>0,e=e>>>0,n||s(t,e,this.length);let r=this[t+--e],o=1;for(;e>0&&(o*=256);)r+=this[t+--e]*o;return r},Buffer.prototype.readUint8=Buffer.prototype.readUInt8=function(t,e){return t=t>>>0,e||s(t,1,this.length),this[t]},Buffer.prototype.readUint16LE=Buffer.prototype.readUInt16LE=function(t,e){return t=t>>>0,e||s(t,2,this.length),this[t]|this[t+1]<<8},Buffer.prototype.readUint16BE=Buffer.prototype.readUInt16BE=function(t,e){return t=t>>>0,e||s(t,2,this.length),this[t]<<8|this[t+1]},Buffer.prototype.readUint32LE=Buffer.prototype.readUInt32LE=function(t,e){return t=t>>>0,e||s(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216},Buffer.prototype.readUint32BE=Buffer.prototype.readUInt32BE=function(t,e){return t=t>>>0,e||s(t,4,this.length),this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])},Buffer.prototype.readBigUInt64LE=x(function(t){t=t>>>0,m(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&g(t,this.length-8);const r=e+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24,o=this[++t]+this[++t]*2**8+this[++t]*2**16+n*2**24;return BigInt(r)+(BigInt(o)<<BigInt(32))}),Buffer.prototype.readBigUInt64BE=x(function(t){t=t>>>0,m(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&g(t,this.length-8);const r=e*2**24+this[++t]*2**16+this[++t]*2**8+this[++t],o=this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+n;return(BigInt(r)<<BigInt(32))+BigInt(o)}),Buffer.prototype.readIntLE=function(t,e,n){t=t>>>0,e=e>>>0,n||s(t,e,this.length);let r=this[t],o=1,f=0;for(;++f<e&&(o*=256);)r+=this[t+f]*o;return o*=128,r>=o&&(r-=Math.pow(2,8*e)),r},Buffer.prototype.readIntBE=function(t,e,n){t=t>>>0,e=e>>>0,n||s(t,e,this.length);let r=e,o=1,f=this[t+--r];for(;r>0&&(o*=256);)f+=this[t+--r]*o;return o*=128,f>=o&&(f-=Math.pow(2,8*e)),f},Buffer.prototype.readInt8=function(t,e){return t=t>>>0,e||s(t,1,this.length),this[t]&128?(255-this[t]+1)*-1:this[t]},Buffer.prototype.readInt16LE=function(t,e){t=t>>>0,e||s(t,2,this.length);const n=this[t]|this[t+1]<<8;return n&32768?n|4294901760:n},Buffer.prototype.readInt16BE=function(t,e){t=t>>>0,e||s(t,2,this.length);const n=this[t+1]|this[t]<<8;return n&32768?n|4294901760:n},Buffer.prototype.readInt32LE=function(t,e){return t=t>>>0,e||s(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},Buffer.prototype.readInt32BE=function(t,e){return t=t>>>0,e||s(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},Buffer.prototype.readBigInt64LE=x(function(t){t=t>>>0,m(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&g(t,this.length-8);const r=this[t+4]+this[t+5]*2**8+this[t+6]*2**16+(n<<24);return(BigInt(r)<<BigInt(32))+BigInt(e+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24)}),Buffer.prototype.readBigInt64BE=x(function(t){t=t>>>0,m(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&g(t,this.length-8);const r=(e<<24)+this[++t]*2**16+this[++t]*2**8+this[++t];return(BigInt(r)<<BigInt(32))+BigInt(this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+n)}),Buffer.prototype.readFloatLE=function(t,e){return t=t>>>0,e||s(t,4,this.length),read(this,t,!0,23,4)},Buffer.prototype.readFloatBE=function(t,e){return t=t>>>0,e||s(t,4,this.length),read(this,t,!1,23,4)},Buffer.prototype.readDoubleLE=function(t,e){return t=t>>>0,e||s(t,8,this.length),read(this,t,!0,52,8)},Buffer.prototype.readDoubleBE=function(t,e){return t=t>>>0,e||s(t,8,this.length),read(this,t,!1,52,8)};function y(t,e,n,r,o,f){if(!Buffer.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<f)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}Buffer.prototype.writeUintLE=Buffer.prototype.writeUIntLE=function(t,e,n,r){if(t=+t,e=e>>>0,n=n>>>0,!r){const h=Math.pow(2,8*n)-1;y(this,t,e,n,h,0)}let o=1,f=0;for(this[e]=t&255;++f<n&&(o*=256);)this[e+f]=t/o&255;return e+n},Buffer.prototype.writeUintBE=Buffer.prototype.writeUIntBE=function(t,e,n,r){if(t=+t,e=e>>>0,n=n>>>0,!r){const h=Math.pow(2,8*n)-1;y(this,t,e,n,h,0)}let o=n-1,f=1;for(this[e+o]=t&255;--o>=0&&(f*=256);)this[e+o]=t/f&255;return e+n},Buffer.prototype.writeUint8=Buffer.prototype.writeUInt8=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,1,255,0),this[e]=t&255,e+1},Buffer.prototype.writeUint16LE=Buffer.prototype.writeUInt16LE=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,2,65535,0),this[e]=t&255,this[e+1]=t>>>8,e+2},Buffer.prototype.writeUint16BE=Buffer.prototype.writeUInt16BE=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=t&255,e+2},Buffer.prototype.writeUint32LE=Buffer.prototype.writeUInt32LE=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=t&255,e+4},Buffer.prototype.writeUint32BE=Buffer.prototype.writeUInt32BE=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4};function G(t,e,n,r,o){X(e,r,o,t,n,7);let f=Number(e&BigInt(4294967295));t[n++]=f,f=f>>8,t[n++]=f,f=f>>8,t[n++]=f,f=f>>8,t[n++]=f;let h=Number(e>>BigInt(32)&BigInt(4294967295));return t[n++]=h,h=h>>8,t[n++]=h,h=h>>8,t[n++]=h,h=h>>8,t[n++]=h,n}function Y(t,e,n,r,o){X(e,r,o,t,n,7);let f=Number(e&BigInt(4294967295));t[n+7]=f,f=f>>8,t[n+6]=f,f=f>>8,t[n+5]=f,f=f>>8,t[n+4]=f;let h=Number(e>>BigInt(32)&BigInt(4294967295));return t[n+3]=h,h=h>>8,t[n+2]=h,h=h>>8,t[n+1]=h,h=h>>8,t[n]=h,n+8}Buffer.prototype.writeBigUInt64LE=x(function(t,e=0){return G(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),Buffer.prototype.writeBigUInt64BE=x(function(t,e=0){return Y(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),Buffer.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e=e>>>0,!r){const E=Math.pow(2,8*n-1);y(this,t,e,n,E-1,-E)}let o=0,f=1,h=0;for(this[e]=t&255;++o<n&&(f*=256);)t<0&&h===0&&this[e+o-1]!==0&&(h=1),this[e+o]=Math.trunc(t/f)-h&255;return e+n},Buffer.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e=e>>>0,!r){const E=Math.pow(2,8*n-1);y(this,t,e,n,E-1,-E)}let o=n-1,f=1,h=0;for(this[e+o]=t&255;--o>=0&&(f*=256);)t<0&&h===0&&this[e+o+1]!==0&&(h=1),this[e+o]=Math.trunc(t/f)-h&255;return e+n},Buffer.prototype.writeInt8=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=t&255,e+1},Buffer.prototype.writeInt16LE=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,2,32767,-32768),this[e]=t&255,this[e+1]=t>>>8,e+2},Buffer.prototype.writeInt16BE=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=t&255,e+2},Buffer.prototype.writeInt32LE=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,4,2147483647,-2147483648),this[e]=t&255,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},Buffer.prototype.writeInt32BE=function(t,e,n){return t=+t,e=e>>>0,n||y(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4},Buffer.prototype.writeBigInt64LE=x(function(t,e=0){return G(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),Buffer.prototype.writeBigInt64BE=x(function(t,e=0){return Y(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function W(t,e,n,r,o,f){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function q(t,e,n,r,o){return e=+e,n=n>>>0,o||W(t,e,n,4),write(t,e,n,r,23,4),n+4}Buffer.prototype.writeFloatLE=function(t,e,n){return q(this,t,e,!0,n)},Buffer.prototype.writeFloatBE=function(t,e,n){return q(this,t,e,!1,n)};function V(t,e,n,r,o){return e=+e,n=n>>>0,o||W(t,e,n,8),write(t,e,n,r,52,8),n+8}Buffer.prototype.writeDoubleLE=function(t,e,n){return V(this,t,e,!0,n)},Buffer.prototype.writeDoubleBE=function(t,e,n){return V(this,t,e,!1,n)},Buffer.prototype.copy=function(t,e,n,r){if(!Buffer.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),!r&&r!==0&&(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n||t.length===0||this.length===0)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);const o=r-n;return this===t&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(e,n,r):Uint8Array.prototype.set.call(t,this.subarray(n,r),e),o},Buffer.prototype.fill=function(t,e,n,r){if(typeof t=="string"){if(typeof e=="string"?(r=e,e=0,n=this.length):typeof n=="string"&&(r=n,n=this.length),r!==void 0&&typeof r!="string")throw new TypeError("encoding must be a string");if(typeof r=="string"&&!Buffer.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(t.length===1){const f=t.charCodeAt(0);(r==="utf8"&&f<128||r==="latin1")&&(t=f)}}else typeof t=="number"?t=t&255:typeof t=="boolean"&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;e=e>>>0,n=n===void 0?this.length:n>>>0,t||(t=0);let o;if(typeof t=="number")for(o=e;o<n;++o)this[o]=t;else{const f=Buffer.isBuffer(t)?t:Buffer.from(t,r),h=f.length;if(h===0)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<n-e;++o)this[o+e]=f[o%h]}return this};const _={};function S(t,e,n){_[t]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:Reflect.apply(e,this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(r){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:r,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}S("ERR_BUFFER_OUT_OF_BOUNDS",function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),S("ERR_INVALID_ARG_TYPE",function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`},TypeError),S("ERR_OUT_OF_RANGE",function(t,e,n){let r=`The value of "${t}" is out of range.`,o=n;return Number.isInteger(n)&&Math.abs(n)>2**32?o=j(String(n)):typeof n=="bigint"&&(o=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(o=j(o)),o+="n"),r+=` It must be ${e}. Received ${o}`,r},RangeError);function j(t){let e="",n=t.length;const r=t[0]==="-"?1:0;for(;n>=r+4;n-=3)e=`_${t.slice(n-3,n)}${e}`;return`${t.slice(0,n)}${e}`}function lr(t,e,n){m(e,"offset"),(t[e]===void 0||t[e+n]===void 0)&&g(e,t.length-(n+1))}function X(t,e,n,r,o,f){if(t>n||t<e){const h=typeof e=="bigint"?"n":"";let E;throw E=e===0||e===BigInt(0)?`>= 0${h} and < 2${h} ** ${(f+1)*8}${h}`:`>= -(2${h} ** ${(f+1)*8-1}${h}) and < 2 ** ${(f+1)*8-1}${h}`,new _.ERR_OUT_OF_RANGE("value",E,t)}lr(r,o,f)}function m(t,e){if(typeof t!="number")throw new _.ERR_INVALID_ARG_TYPE(e,"number",t)}function g(t,e,n){throw Math.floor(t)!==t?(m(t,n),new _.ERR_OUT_OF_RANGE("offset","an integer",t)):e<0?new _.ERR_BUFFER_OUT_OF_BOUNDS:new _.ERR_OUT_OF_RANGE("offset",`>= 0 and <= ${e}`,t)}const sr=/[^\w+/-]/g;function yr(t){if(t=t.split("=")[0],t=t.trim().replace(sr,""),t.length<2)return"";for(;t.length%4!==0;)t=t+"=";return t}function L(t,e){e=e||Number.POSITIVE_INFINITY;let n;const r=t.length;let o=null;const f=[];for(let h=0;h<r;++h){if(n=t.charCodeAt(h),n>55295&&n<57344){if(!o){if(n>56319){(e-=3)>-1&&f.push(239,191,189);continue}else if(h+1===r){(e-=3)>-1&&f.push(239,191,189);continue}o=n;continue}if(n<56320){(e-=3)>-1&&f.push(239,191,189),o=n;continue}n=(o-55296<<10|n-56320)+65536}else o&&(e-=3)>-1&&f.push(239,191,189);if(o=null,n<128){if((e-=1)<0)break;f.push(n)}else if(n<2048){if((e-=2)<0)break;f.push(n>>6|192,n&63|128)}else if(n<65536){if((e-=3)<0)break;f.push(n>>12|224,n>>6&63|128,n&63|128)}else if(n<1114112){if((e-=4)<0)break;f.push(n>>18|240,n>>12&63|128,n>>6&63|128,n&63|128)}else throw new Error("Invalid code point")}return f}function ar(t){const e=[];for(let n=0;n<t.length;++n)e.push(t.charCodeAt(n)&255);return e}function wr(t,e){let n,r,o;const f=[];for(let h=0;h<t.length&&!((e-=2)<0);++h)n=t.charCodeAt(h),r=n>>8,o=n%256,f.push(o,r);return f}function H(t){return toByteArray(yr(t))}function d(t,e,n,r){let o;for(o=0;o<r&&!(o+n>=e.length||o>=t.length);++o)e[o+n]=t[o];return o}function a(t,e){return t instanceof e||t!=null&&t.constructor!=null&&t.constructor.name!=null&&t.constructor.name===e.name}function N(t){return t!==t}const xr=function(){const t="0123456789abcdef",e=Array.from({length:256});for(let n=0;n<16;++n){const r=n*16;for(let o=0;o<16;++o)e[r+o]=t[n]+t[o]}return e}();function x(t){return typeof BigInt>"u"?Br:t}function Br(){throw new Error("BigInt not supported")}exports.Buffer=Buffer;
