let v=10;const Y=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),F=(t,e)=>t,_=Error,Z=Error,h=Error,w=Error,ee=Error,R=Symbol.for("nodejs.rejection"),a=Symbol.for("kCapture"),M=Symbol.for("events.errorMonitor"),g=Symbol.for("shapeMode"),b=Symbol.for("events.maxEventTargetListeners"),te=Symbol.for("kEnhanceStackBeforeInspector"),ne=Symbol.for("nodejs.watermarkData"),d=Symbol.for("kAsyncResource"),re=Symbol.for("kFirstEventParam"),S=Symbol.for("kResistStopPropagation"),W=Symbol.for("events.maxEventTargetListenersWarned");class m{_events=void 0;_eventsCount=0;_maxListeners=v;[a]=!1;[g]=!1;static captureRejectionSymbol=R;static errorMonitor=M;static kMaxEventTargetListeners=b;static kMaxEventTargetListenersWarned=W;static usingDomains=!1;static get on(){return ie}static get once(){return oe}static get getEventListeners(){return ue}static get getMaxListeners(){return ce}static get addAbortListener(){return $}static get EventEmitterAsyncResource(){return se}static get EventEmitter(){return m}static setMaxListeners(e=v,...n){if(n.length===0)v=e;else for(const r of n)if(q(r))r[b]=e,r[W]=!1;else if(typeof r.setMaxListeners=="function")r.setMaxListeners(e);else throw new h("eventTargets",["EventEmitter","EventTarget"],r)}static listenerCount(e,n){if(typeof e.listenerCount=="function")return e.listenerCount(n);m.prototype.listenerCount.call(e,n)}static init(){throw new Error("EventEmitter.init() is not implemented.")}static get captureRejections(){return this[a]}static set captureRejections(e){this[a]=e}static get defaultMaxListeners(){return v}static set defaultMaxListeners(e){v=e}constructor(e){this._events===void 0||this._events===Object.getPrototypeOf(this)._events?(this._events={__proto__:null},this._eventsCount=0,this[g]=!1):this[g]=!0,this._maxListeners=this._maxListeners||void 0,e?.captureRejections?this[a]=!!e.captureRejections:this[a]=m.prototype[a]}setMaxListeners(e){return this._maxListeners=e,this}getMaxListeners(){return j(this)}emit(e,...n){let r=e==="error";const s=this._events;if(s!==void 0)r&&s[M]!==void 0&&this.emit(M,...n),r=r&&s.error===void 0;else if(!r)return!1;if(r){let i;if(n.length>0&&(i=n[0]),i instanceof Error){try{const l={};Error.captureStackTrace?.(l,m.prototype.emit),Object.defineProperty(i,te,{__proto__:null,value:Function.prototype.bind(le,this,i,l),configurable:!0})}catch{}throw i}let c;try{c=F(i)}catch{c=i}const f=new Z(c);throw f.context=i,f}const o=s[e];if(o===void 0)return!1;if(typeof o=="function"){const i=o.apply(this,n);i!=null&&z(this,i,e,n)}else{const i=o.length,c=C(o);for(let f=0;f<i;++f){const l=c[f].apply(this,n);l!=null&&z(this,l,e,n)}}return!0}addListener(e,n){return B(this,e,n,!1),this}on(e,n){return this.addListener(e,n)}prependListener(e,n){return B(this,e,n,!0),this}once(e,n){return this.on(e,G(this,e,n)),this}prependOnceListener(e,n){return this.prependListener(e,G(this,e,n)),this}removeListener(e,n){const r=this._events;if(r===void 0)return this;const s=r[e];if(s===void 0)return this;if(s===n||s.listener===n)this._eventsCount-=1,this[g]?r[e]=void 0:this._eventsCount===0?this._events={__proto__:null}:(delete r[e],r.removeListener&&this.emit("removeListener",e,s.listener||n));else if(typeof s!="function"){let o=-1;for(let i=s.length-1;i>=0;i--)if(s[i]===n||s[i].listener===n){o=i;break}if(o<0)return this;o===0?s.shift():me(s,o),s.length===1&&(r[e]=s[0]),r.removeListener!==void 0&&this.emit("removeListener",e,n)}return this}off(e,n){return this.removeListener(e,n)}removeAllListeners(e){const n=this._events;if(n===void 0)return this;if(n.removeListener===void 0)return arguments.length===0?(this._events={__proto__:null},this._eventsCount=0):n[e]!==void 0&&(--this._eventsCount===0?this._events={__proto__:null}:delete n[e]),this[g]=!1,this;if(arguments.length===0){for(const s of Reflect.ownKeys(n))s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events={__proto__:null},this._eventsCount=0,this[g]=!1,this}const r=n[e];if(typeof r=="function")this.removeListener(e,r);else if(r!==void 0)for(let s=r.length-1;s>=0;s--)this.removeListener(e,r[s]);return this}listeners(e){return U(this,e,!0)}rawListeners(e){return U(this,e,!1)}eventNames(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]}listenerCount(e,n){const r=this._events;if(r!==void 0){const s=r[e];if(typeof s=="function")return n!=null?n===s||n===s.listener?1:0:1;if(s!==void 0){if(n!=null){let o=0;for(let i=0,c=s.length;i<c;i++)(s[i]===n||s[i].listener===n)&&o++;return o}return s.length}}return 0}}class se extends m{constructor(e){let n;typeof e=="string"?(n=e,e=void 0):n=e?.name||new.target.name,super(e),this[d]=new EventEmitterReferencingAsyncResource(this,n,e)}emit(e,...n){if(this[d]===void 0)throw new _("EventEmitterAsyncResource");const{asyncResource:r}=this;return Array.prototype.unshift(n,super.emit,this,e),Reflect.apply(r.runInAsyncScope,r,n)}emitDestroy(){if(this[d]===void 0)throw new _("EventEmitterAsyncResource");this.asyncResource.emitDestroy()}get asyncId(){if(this[d]===void 0)throw new _("EventEmitterAsyncResource");return this.asyncResource.asyncId()}get triggerAsyncId(){if(this[d]===void 0)throw new _("EventEmitterAsyncResource");return this.asyncResource.triggerAsyncId()}get asyncResource(){if(this[d]===void 0)throw new _("EventEmitterAsyncResource");return this[d]}}const ie=function(t,e,n={}){const r=n.signal;if(r?.aborted)throw new w(void 0,{cause:r?.reason});const s=n.highWaterMark??n.highWatermark??Number.MAX_SAFE_INTEGER,o=n.lowWaterMark??n.lowWatermark??1,i=new K,c=new K;let f=!1,l=null,p=!1,y=0;const H=Object.setPrototypeOf({next(){if(y){const u=i.shift();return y--,f&&y<o&&(t.resume?.(),f=!1),Promise.resolve(P(u,!1))}if(l){const u=Promise.reject(l);return l=null,u}return p?L():new Promise(function(u,X){c.push({resolve:u,reject:X})})},return(){return L()},throw(u){if(!u||!(u instanceof Error))throw new h("EventEmitter.AsyncIterator","Error",u);A(u)},[Symbol.asyncIterator](){return this},[ne]:{get size(){return y},get low(){return o},get high(){return s},get isPaused(){return f}}},Y),{addEventListener:x,removeAll:J}=de();x(t,e,n[re]?I:function(...u){return I(u)}),e!=="error"&&typeof t.on=="function"&&x(t,"error",A);const O=n?.close;if(O?.length)for(const u of O)x(t,u,L);const Q=r?$(r,V):null;return H;function V(){A(new w(void 0,{cause:r?.reason}))}function I(u){c.isEmpty()?(y++,!f&&y>s&&(f=!0,t.pause?.()),i.push(u)):c.shift().resolve(P(u,!1))}function A(u){c.isEmpty()?l=u:c.shift().reject(u),L()}function L(){Q?.[Symbol.dispose](),J(),p=!0;const u=P(void 0,!0);for(;!c.isEmpty();)c.shift().resolve(u);return Promise.resolve(u)}},oe=async function(t,e,n={}){const r=n?.signal;if(r?.aborted)throw new w(void 0,{cause:r?.reason});return new Promise((s,o)=>{const i=p=>{typeof t.removeListener=="function"&&t.removeListener(e,c),r!=null&&E(r,"abort",l),o(p)},c=(...p)=>{typeof t.removeListener=="function"&&t.removeListener("error",i),r!=null&&E(r,"abort",l),s(p)},f={__proto__:null,once:!0,[S]:!0};T(t,e,c,f),e!=="error"&&typeof t.once=="function"&&t.once("error",i);function l(){E(t,e,c),E(t,"error",i),o(new w(void 0,{cause:r?.reason}))}r!=null&&T(r,"abort",l,{__proto__:null,once:!0,[S]:!0})})},$=function(t,e){if(t===void 0)throw new h("signal","AbortSignal",t);let n;return t.aborted?queueMicrotask(()=>e()):(t.addEventListener("abort",e,{__proto__:null,once:!0,[S]:!0}),n=()=>{t.removeEventListener("abort",e)}),{__proto__:null,[Symbol.dispose](){n?.()}}},ue=function(t,e){if(typeof t.listeners=="function")return t.listeners(e);if(q(t)){const n=t[kEvents].get(e),r=[];let s=n?.next;for(;s?.listener!==void 0;){const o=s.listener?.deref?s.listener.deref():s.listener;r.push(o),s=s.next}return r}throw new h("emitter",["EventEmitter","EventTarget"],t)},ce=function(t){if(typeof t?.getMaxListeners=="function")return j(t);if(t?.[b])return t[b];throw new h("emitter",["EventEmitter","EventTarget"],t)},D=2048,k=D-1;class N{bottom;top;list;next;constructor(){this.bottom=0,this.top=0,this.list=new Array(D),this.next=null}isEmpty(){return this.top===this.bottom}isFull(){return(this.top+1&k)===this.bottom}push(e){this.list[this.top]=e,this.top=this.top+1&k}shift(){const e=this.list[this.bottom];return e===void 0?null:(this.list[this.bottom]=void 0,this.bottom=this.bottom+1&k,e)}}class K{head;tail;constructor(){this.head=this.tail=new N}isEmpty(){return this.head.isEmpty()}push(e){this.head.isFull()&&(this.head=this.head.next=new N),this.head.push(e)}shift(){const e=this.tail,n=e.shift();return e.isEmpty()&&e.next!==null&&(this.tail=e.next,e.next=null),n}}function q(t){return typeof t?.addEventListener=="function"}function z(t,e,n,r){if(t[a])try{const s=e.then;typeof s=="function"&&s.call(e,void 0,function(o){process.nextTick(fe,t,o,n,r)})}catch(s){t.emit("error",s)}}function fe(t,e,n,r){if(typeof t[R]=="function")t[R](e,n,...r);else{const s=t[a];try{t[a]=!1,t.emit("error",e)}finally{t[a]=s}}}function j(t){return t._maxListeners===void 0?v:t._maxListeners}function le(t,e){let n="";try{const{name:o}=this.constructor;o!=="EventEmitter"&&(n=` on ${o} instance`)}catch{}const r=`
Emitted 'error' event${n} at:
`,s=(e.stack||"").split(`
`).slice(1);return t.stack+r+s.join(`
`)}function B(t,e,n,r){let s,o,i;if(o=t._events,o===void 0?(o=t._events={__proto__:null},t._eventsCount=0):(o.newListener!==void 0&&(t.emit("newListener",e,n.listener??n),o=t._events),i=o[e]),i===void 0)o[e]=n,++t._eventsCount;else if(typeof i=="function"?i=o[e]=r?[n,i]:[i,n]:r?i.unshift(n):i.push(n),s=j(t),s>0&&i.length>s&&!i.warned){i.warned=!0;const c=new ee(`Possible EventEmitter memory leak detected. ${i.length} ${String(e)} listeners added to ${F(t)}. MaxListeners is ${s}. Use emitter.setMaxListeners() to increase limit`,{name:"MaxListenersExceededWarning",emitter:t,type:e,count:i.length});process.emitWarning(c)}return t}function ae(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function G(t,e,n){const r={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},s=ae.bind(r);return s.listener=n,r.wrapFn=s,s}function U(t,e,n){const r=t._events;if(r===void 0)return[];const s=r[e];return s===void 0?[]:typeof s=="function"?n?[s.listener||s]:[s]:n?he(s):C(s)}function C(t){switch(t.length){case 2:return[t[0],t[1]];case 3:return[t[0],t[1],t[2]];case 4:return[t[0],t[1],t[2],t[3]];case 5:return[t[0],t[1],t[2],t[3],t[4]];case 6:return[t[0],t[1],t[2],t[3],t[4],t[5]]}return Array.prototype.slice(t)}function he(t){const e=C(t);for(let n=0;n<e.length;++n){const r=e[n].listener;typeof r=="function"&&(e[n]=r)}return e}function P(t,e){return{value:t,done:e}}function E(t,e,n,r){if(typeof t.removeListener=="function")t.removeListener(e,n);else if(typeof t.removeEventListener=="function")t.removeEventListener(e,n,r);else throw new h("emitter","EventEmitter",t)}function T(t,e,n,r){if(typeof t.on=="function")r?.once?t.once(e,n):t.on(e,n);else if(typeof t.addEventListener=="function")t.addEventListener(e,n,r);else throw new h("emitter","EventEmitter",t)}function de(){const t=[];return{addEventListener(e,n,r,s){T(e,n,r,s),Array.prototype.push(t,[e,n,r,s])},removeAll(){for(;t.length>0;)Reflect.apply(E,void 0,t.pop())}}}function me(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}export{m as EventEmitter};
