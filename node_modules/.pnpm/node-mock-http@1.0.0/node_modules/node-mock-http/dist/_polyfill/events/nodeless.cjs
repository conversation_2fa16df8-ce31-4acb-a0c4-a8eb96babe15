"use strict";let y=10;const Y=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),F=(t,e)=>t,g=Error,J=Error,h=Error,b=Error,Z=Error,R=Symbol.for("nodejs.rejection"),l=Symbol.for("kCapture"),A=Symbol.for("events.errorMonitor"),p=Symbol.for("shapeMode"),w=Symbol.for("events.maxEventTargetListeners"),ee=Symbol.for("kEnhanceStackBeforeInspector"),te=Symbol.for("nodejs.watermarkData"),E=Symbol.for("kAsyncResource"),ne=Symbol.for("kFirstEventParam"),N=Symbol.for("kResistStopPropagation"),W=Symbol.for("events.maxEventTargetListenersWarned");class EventEmitter{_events=void 0;_eventsCount=0;_maxListeners=y;[l]=!1;[p]=!1;static captureRejectionSymbol=R;static errorMonitor=A;static kMaxEventTargetListeners=w;static kMaxEventTargetListenersWarned=W;static usingDomains=!1;static get on(){return on}static get once(){return once}static get getEventListeners(){return getEventListeners}static get getMaxListeners(){return getMaxListeners}static get addAbortListener(){return addAbortListener}static get EventEmitterAsyncResource(){return EventEmitterAsyncResource}static get EventEmitter(){return EventEmitter}static setMaxListeners(e=y,...n){if(n.length===0)y=e;else for(const r of n)if(H(r))r[w]=e,r[W]=!1;else if(typeof r.setMaxListeners=="function")r.setMaxListeners(e);else throw new h("eventTargets",["EventEmitter","EventTarget"],r)}static listenerCount(e,n){if(typeof e.listenerCount=="function")return e.listenerCount(n);EventEmitter.prototype.listenerCount.call(e,n)}static init(){throw new Error("EventEmitter.init() is not implemented.")}static get captureRejections(){return this[l]}static set captureRejections(e){this[l]=e}static get defaultMaxListeners(){return y}static set defaultMaxListeners(e){y=e}constructor(e){this._events===void 0||this._events===Object.getPrototypeOf(this)._events?(this._events={__proto__:null},this._eventsCount=0,this[p]=!1):this[p]=!0,this._maxListeners=this._maxListeners||void 0,e?.captureRejections?this[l]=!!e.captureRejections:this[l]=EventEmitter.prototype[l]}setMaxListeners(e){return this._maxListeners=e,this}getMaxListeners(){return S(this)}emit(e,...n){let r=e==="error";const s=this._events;if(s!==void 0)r&&s[A]!==void 0&&this.emit(A,...n),r=r&&s.error===void 0;else if(!r)return!1;if(r){let i;if(n.length>0&&(i=n[0]),i instanceof Error){try{const a={};Error.captureStackTrace?.(a,EventEmitter.prototype.emit),Object.defineProperty(i,ee,{__proto__:null,value:Function.prototype.bind(re,this,i,a),configurable:!0})}catch{}throw i}let c;try{c=F(i)}catch{c=i}const f=new J(c);throw f.context=i,f}const o=s[e];if(o===void 0)return!1;if(typeof o=="function"){const i=o.apply(this,n);i!=null&&O(this,i,e,n)}else{const i=o.length,c=C(o);for(let f=0;f<i;++f){const a=c[f].apply(this,n);a!=null&&O(this,a,e,n)}}return!0}addListener(e,n){return z(this,e,n,!1),this}on(e,n){return this.addListener(e,n)}prependListener(e,n){return z(this,e,n,!0),this}once(e,n){return this.on(e,U(this,e,n)),this}prependOnceListener(e,n){return this.prependListener(e,U(this,e,n)),this}removeListener(e,n){const r=this._events;if(r===void 0)return this;const s=r[e];if(s===void 0)return this;if(s===n||s.listener===n)this._eventsCount-=1,this[p]?r[e]=void 0:this._eventsCount===0?this._events={__proto__:null}:(delete r[e],r.removeListener&&this.emit("removeListener",e,s.listener||n));else if(typeof s!="function"){let o=-1;for(let i=s.length-1;i>=0;i--)if(s[i]===n||s[i].listener===n){o=i;break}if(o<0)return this;o===0?s.shift():ce(s,o),s.length===1&&(r[e]=s[0]),r.removeListener!==void 0&&this.emit("removeListener",e,n)}return this}off(e,n){return this.removeListener(e,n)}removeAllListeners(e){const n=this._events;if(n===void 0)return this;if(n.removeListener===void 0)return arguments.length===0?(this._events={__proto__:null},this._eventsCount=0):n[e]!==void 0&&(--this._eventsCount===0?this._events={__proto__:null}:delete n[e]),this[p]=!1,this;if(arguments.length===0){for(const s of Reflect.ownKeys(n))s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events={__proto__:null},this._eventsCount=0,this[p]=!1,this}const r=n[e];if(typeof r=="function")this.removeListener(e,r);else if(r!==void 0)for(let s=r.length-1;s>=0;s--)this.removeListener(e,r[s]);return this}listeners(e){return G(this,e,!0)}rawListeners(e){return G(this,e,!1)}eventNames(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]}listenerCount(e,n){const r=this._events;if(r!==void 0){const s=r[e];if(typeof s=="function")return n!=null?n===s||n===s.listener?1:0:1;if(s!==void 0){if(n!=null){let o=0;for(let i=0,c=s.length;i<c;i++)(s[i]===n||s[i].listener===n)&&o++;return o}return s.length}}return 0}}class EventEmitterAsyncResource extends EventEmitter{constructor(e){let n;typeof e=="string"?(n=e,e=void 0):n=e?.name||new.target.name,super(e),this[E]=new EventEmitterReferencingAsyncResource(this,n,e)}emit(e,...n){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");const{asyncResource:r}=this;return Array.prototype.unshift(n,super.emit,this,e),Reflect.apply(r.runInAsyncScope,r,n)}emitDestroy(){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");this.asyncResource.emitDestroy()}get asyncId(){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");return this.asyncResource.asyncId()}get triggerAsyncId(){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");return this.asyncResource.triggerAsyncId()}get asyncResource(){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");return this[E]}}const on=function(t,e,n={}){const r=n.signal;if(r?.aborted)throw new b(void 0,{cause:r?.reason});const s=n.highWaterMark??n.highWatermark??Number.MAX_SAFE_INTEGER,o=n.lowWaterMark??n.lowWatermark??1,i=new $,c=new $;let f=!1,a=null,d=!1,m=0;const I=Object.setPrototypeOf({next(){if(m){const u=i.shift();return m--,f&&m<o&&(t.resume?.(),f=!1),Promise.resolve(T(u,!1))}if(a){const u=Promise.reject(a);return a=null,u}return d?v():new Promise(function(u,V){c.push({resolve:u,reject:V})})},return(){return v()},throw(u){if(!u||!(u instanceof Error))throw new h("EventEmitter.AsyncIterator","Error",u);x(u)},[Symbol.asyncIterator](){return this},[te]:{get size(){return m},get low(){return o},get high(){return s},get isPaused(){return f}}},Y),{addEventListener:_,removeAll:K}=ue();_(t,e,n[ne]?j:function(...u){return j(u)}),e!=="error"&&typeof t.on=="function"&&_(t,"error",x);const k=n?.close;if(k?.length)for(const u of k)_(t,u,v);const q=r?addAbortListener(r,Q):null;return I;function Q(){x(new b(void 0,{cause:r?.reason}))}function j(u){c.isEmpty()?(m++,!f&&m>s&&(f=!0,t.pause?.()),i.push(u)):c.shift().resolve(T(u,!1))}function x(u){c.isEmpty()?a=u:c.shift().reject(u),v()}function v(){q?.[Symbol.dispose](),K(),d=!0;const u=T(void 0,!0);for(;!c.isEmpty();)c.shift().resolve(u);return Promise.resolve(u)}},once=async function(t,e,n={}){const r=n?.signal;if(r?.aborted)throw new b(void 0,{cause:r?.reason});return new Promise((s,o)=>{const i=d=>{typeof t.removeListener=="function"&&t.removeListener(e,c),r!=null&&L(r,"abort",a),o(d)},c=(...d)=>{typeof t.removeListener=="function"&&t.removeListener("error",i),r!=null&&L(r,"abort",a),s(d)},f={__proto__:null,once:!0,[N]:!0};P(t,e,c,f),e!=="error"&&typeof t.once=="function"&&t.once("error",i);function a(){L(t,e,c),L(t,"error",i),o(new b(void 0,{cause:r?.reason}))}r!=null&&P(r,"abort",a,{__proto__:null,once:!0,[N]:!0})})},addAbortListener=function(t,e){if(t===void 0)throw new h("signal","AbortSignal",t);let n;return t.aborted?queueMicrotask(()=>e()):(t.addEventListener("abort",e,{__proto__:null,once:!0,[N]:!0}),n=()=>{t.removeEventListener("abort",e)}),{__proto__:null,[Symbol.dispose](){n?.()}}},getEventListeners=function(t,e){if(typeof t.listeners=="function")return t.listeners(e);if(H(t)){const n=t[kEvents].get(e),r=[];let s=n?.next;for(;s?.listener!==void 0;){const o=s.listener?.deref?s.listener.deref():s.listener;r.push(o),s=s.next}return r}throw new h("emitter",["EventEmitter","EventTarget"],t)},getMaxListeners=function(t){if(typeof t?.getMaxListeners=="function")return S(t);if(t?.[w])return t[w];throw new h("emitter",["EventEmitter","EventTarget"],t)},D=2048,M=D-1;class B{bottom;top;list;next;constructor(){this.bottom=0,this.top=0,this.list=new Array(D),this.next=null}isEmpty(){return this.top===this.bottom}isFull(){return(this.top+1&M)===this.bottom}push(e){this.list[this.top]=e,this.top=this.top+1&M}shift(){const e=this.list[this.bottom];return e===void 0?null:(this.list[this.bottom]=void 0,this.bottom=this.bottom+1&M,e)}}class ${head;tail;constructor(){this.head=this.tail=new B}isEmpty(){return this.head.isEmpty()}push(e){this.head.isFull()&&(this.head=this.head.next=new B),this.head.push(e)}shift(){const e=this.tail,n=e.shift();return e.isEmpty()&&e.next!==null&&(this.tail=e.next,e.next=null),n}}function H(t){return typeof t?.addEventListener=="function"}function O(t,e,n,r){if(t[l])try{const s=e.then;typeof s=="function"&&s.call(e,void 0,function(o){process.nextTick(se,t,o,n,r)})}catch(s){t.emit("error",s)}}function se(t,e,n,r){if(typeof t[R]=="function")t[R](e,n,...r);else{const s=t[l];try{t[l]=!1,t.emit("error",e)}finally{t[l]=s}}}function S(t){return t._maxListeners===void 0?y:t._maxListeners}function re(t,e){let n="";try{const{name:o}=this.constructor;o!=="EventEmitter"&&(n=` on ${o} instance`)}catch{}const r=`
Emitted 'error' event${n} at:
`,s=(e.stack||"").split(`
`).slice(1);return t.stack+r+s.join(`
`)}function z(t,e,n,r){let s,o,i;if(o=t._events,o===void 0?(o=t._events={__proto__:null},t._eventsCount=0):(o.newListener!==void 0&&(t.emit("newListener",e,n.listener??n),o=t._events),i=o[e]),i===void 0)o[e]=n,++t._eventsCount;else if(typeof i=="function"?i=o[e]=r?[n,i]:[i,n]:r?i.unshift(n):i.push(n),s=S(t),s>0&&i.length>s&&!i.warned){i.warned=!0;const c=new Z(`Possible EventEmitter memory leak detected. ${i.length} ${String(e)} listeners added to ${F(t)}. MaxListeners is ${s}. Use emitter.setMaxListeners() to increase limit`,{name:"MaxListenersExceededWarning",emitter:t,type:e,count:i.length});process.emitWarning(c)}return t}function ie(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function U(t,e,n){const r={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},s=ie.bind(r);return s.listener=n,r.wrapFn=s,s}function G(t,e,n){const r=t._events;if(r===void 0)return[];const s=r[e];return s===void 0?[]:typeof s=="function"?n?[s.listener||s]:[s]:n?oe(s):C(s)}function C(t){switch(t.length){case 2:return[t[0],t[1]];case 3:return[t[0],t[1],t[2]];case 4:return[t[0],t[1],t[2],t[3]];case 5:return[t[0],t[1],t[2],t[3],t[4]];case 6:return[t[0],t[1],t[2],t[3],t[4],t[5]]}return Array.prototype.slice(t)}function oe(t){const e=C(t);for(let n=0;n<e.length;++n){const r=e[n].listener;typeof r=="function"&&(e[n]=r)}return e}function T(t,e){return{value:t,done:e}}function L(t,e,n,r){if(typeof t.removeListener=="function")t.removeListener(e,n);else if(typeof t.removeEventListener=="function")t.removeEventListener(e,n,r);else throw new h("emitter","EventEmitter",t)}function P(t,e,n,r){if(typeof t.on=="function")r?.once?t.once(e,n):t.on(e,n);else if(typeof t.addEventListener=="function")t.addEventListener(e,n,r);else throw new h("emitter","EventEmitter",t)}function ue(){const t=[];return{addEventListener(e,n,r,s){P(e,n,r,s),Array.prototype.push(t,[e,n,r,s])},removeAll(){for(;t.length>0;)Reflect.apply(L,void 0,t.pop())}}}function ce(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}exports.EventEmitter=EventEmitter;
