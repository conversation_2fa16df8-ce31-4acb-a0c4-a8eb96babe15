{"name": "node-mock-http", "version": "1.0.0", "description": "", "repository": "unjs/node-mock-http", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./_polyfill/buffer": {"import": {"node": "./dist/_polyfill/buffer/node.mjs", "default": "./dist/_polyfill/buffer/nodeless.mjs"}, "require": {"node": "./dist/_polyfill/buffer/node.cjs", "default": "./dist/_polyfill/buffer/nodeless.cjs"}}, "./_polyfill/events": {"import": {"node": "./dist/_polyfill/events/node.mjs", "default": "./dist/_polyfill/events/nodeless.mjs"}, "require": {"node": "./dist/_polyfill/events/node.cjs", "default": "./dist/_polyfill/events/nodeless.cjs"}}}, "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild --minify", "dev": "vitest dev --coverage", "lint": "eslint . && prettier -c .", "lint:fix": "automd && eslint . --fix && prettier -w .", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm test:types && vitest run --coverage", "test:types": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "devDependencies": {"@types/node": "^22.12.0", "@vitest/coverage-v8": "^3.0.4", "automd": "^0.3.12", "changelogen": "^0.5.7", "eslint": "^9.19.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.4"}, "packageManager": "pnpm@10.2.0"}