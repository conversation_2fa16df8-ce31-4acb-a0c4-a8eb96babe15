{"name": "h3", "version": "1.15.3", "description": "Minimal H(TTP) framework built for high performance and portability.", "repository": "unjs/h3", "license": "MIT", "sideEffects": false, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint --cache . && prettier -c src test playground examples docs", "lint:fix": "eslint --cache . --fix && prettier -c src test playground examples docs -w", "play": "listhen -w ./playground/app.ts", "profile": "0x -o -D .profile -P 'autocannon -c 100 -p 10 -d 40 http://localhost:$PORT' ./playground/server.cjs", "release": "pnpm test && pnpm build && changelogen --release --publish --publishTag latest && git push --follow-tags", "test": "pnpm lint && vitest --run --coverage"}, "resolutions": {"h3": "^1.14.0"}, "dependencies": {"cookie-es": "^1.2.2", "crossws": "^0.3.4", "defu": "^6.1.4", "destr": "^2.0.5", "iron-webcrypto": "^1.2.1", "node-mock-http": "^1.0.0", "radix3": "^1.1.2", "ufo": "^1.6.1", "uncrypto": "^0.1.3"}, "devDependencies": {"0x": "^5.8.0", "@types/express": "^5.0.1", "@types/node": "^22.15.2", "@types/supertest": "^6.0.3", "@vitest/coverage-v8": "^3.1.2", "autocannon": "^8.0.0", "automd": "^0.4.0", "changelogen": "^0.6.1", "connect": "^3.7.0", "eslint": "^9.25.1", "eslint-config-unjs": "^0.4.2", "express": "^5.1.0", "get-port": "^7.1.0", "h3": "^1.15.1", "jiti": "^2.4.2", "listhen": "^1.9.0", "node-fetch-native": "^1.6.6", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "supertest": "^7.1.0", "typescript": "^5.8.3", "unbuild": "^3.5.0", "undici": "^7.8.0", "vitest": "^3.1.2", "zod": "^3.24.3"}, "packageManager": "pnpm@10.2.0"}