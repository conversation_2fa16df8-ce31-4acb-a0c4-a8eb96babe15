import{fetch as e,Blob as t,File as s,FormData as l,Headers as a,Request as i,Response as b,AbortController as m}from"./node.mjs";export{AbortError,FetchError,blobFrom,blobFromSync,fileFrom,fileFromSync,isRedirect}from"./node.mjs";import"node:http";import"node:https";import"node:zlib";import"node:stream";import"node:buffer";import"node:util";import"./shared/node-fetch-native.DfbY2q-x.mjs";import"node:url";import"node:net";import"node:fs";import"node:path";const o=!!globalThis.process?.env?.FORCE_NODE_FETCH,r=!o&&globalThis.fetch||e,p=!o&&globalThis.Blob||t,F=!o&&globalThis.File||s,h=!o&&globalThis.FormData||l,n=!o&&globalThis.Headers||a,c=!o&&globalThis.Request||i,R=!o&&globalThis.Response||b,T=!o&&globalThis.AbortController||m;export{T as AbortController,p as Blob,F as File,h as FormData,n as Headers,c as Request,R as Response,r as default,r as fetch};
