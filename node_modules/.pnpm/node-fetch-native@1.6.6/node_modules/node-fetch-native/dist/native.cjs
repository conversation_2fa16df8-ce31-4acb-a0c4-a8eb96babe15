"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const Blob=globalThis.Blob,File=globalThis.File,FormData=globalThis.FormData,Headers=globalThis.Headers,Request=globalThis.Request,Response=globalThis.Response,AbortController=globalThis.AbortController,fetch=globalThis.fetch||(()=>{throw new Error("[node-fetch-native] Failed to fetch: `globalThis.fetch` is not available!")});exports.AbortController=AbortController,exports.Blob=Blob,exports.File=File,exports.FormData=FormData,exports.Headers=Headers,exports.Request=Request,exports.Response=Response,exports.default=fetch,exports.fetch=fetch;
