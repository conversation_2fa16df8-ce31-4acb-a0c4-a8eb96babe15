{"$schema": "https://json.schemastore.org/package.json", "name": "iron-webcrypto", "version": "1.2.1", "description": "a cryptographic utility for sealing-unsealing a JSON object using symmetric key encryption with message integrity verification", "keywords": ["authentication", "data integrity", "encryption", "webcrypto"], "bugs": {"url": "https://github.com/brc-dd/iron-webcrypto", "email": "<EMAIL>"}, "repository": "github:brc-dd/iron-webcrypto", "funding": "https://github.com/sponsors/brc-dd", "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/brc-dd)", "sideEffects": false, "type": "module", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "main": "dist/index.cjs", "files": ["dist/*"], "scripts": {"build": "pnpm i && tsup && git add -f dist", "lint": "pnpm build && pnpm lint:only", "lint:only": "lefthook run pre-commit && publint && attw --pack", "test": "pnpm test:node && pnpm test:deno && pnpm test:bun", "test:bun": "bun --cwd=tests/bun test", "test:deno": "deno test tests/deno/index.test.ts --parallel --no-check", "test:node": "tsx --test tests/node/index.test.ts"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.3", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@eslint-community/eslint-plugin-eslint-comments": "^4.3.0", "@release-it/conventional-changelog": "^8.0.1", "@types/bun": "^1.1.1", "@types/node": "^20.12.11", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-security": "^3.0.0", "eslint-plugin-unicorn": "^52.0.0", "jsr": "^0.12.4", "lefthook": "^1.6.10", "prettier": "^3.2.5", "prettier-plugin-packagejson": "^2.5.0", "publint": "^0.2.7", "release-it": "^17.2.1", "replace": "^1.2.2", "tsup": "^8.0.2", "tsx": "^4.9.3", "typescript": "^5.4.5"}, "packageManager": "pnpm@9.1.0", "runkitExampleFilename": "dist/example.js"}