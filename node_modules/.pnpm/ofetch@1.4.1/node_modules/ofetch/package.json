{"name": "of<PERSON><PERSON>", "version": "1.4.1", "description": "A better fetch API. Works on node, browser and workers.", "repository": "unjs/ofetch", "license": "MIT", "sideEffects": false, "type": "module", "exports": {"./package.json": "./package.json", ".": {"browser": "./dist/index.mjs", "bun": "./dist/index.mjs", "deno": "./dist/index.mjs", "edge-light": "./dist/index.mjs", "edge-routine": "./dist/index.mjs", "netlify": "./dist/index.mjs", "react-native": "./dist/index.mjs", "wintercg": "./dist/index.mjs", "worker": "./dist/index.mjs", "workerd": "./dist/index.mjs", "node": {"import": {"types": "./dist/node.d.mts", "default": "./dist/node.mjs"}, "require": {"types": "./dist/node.d.cts", "default": "./dist/node.cjs"}}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/node.d.cts", "default": "./dist/node.cjs"}, "types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./node": {"import": {"types": "./dist/node.d.mts", "default": "./dist/node.mjs"}, "require": {"types": "./dist/node.d.cts", "default": "./dist/node.cjs"}}}, "main": "./dist/node.cjs", "module": "./dist/index.mjs", "react-native": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "node.d.ts"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test playground examples", "lint:fix": "eslint --fix . && prettier -w src test playground examples", "prepack": "pnpm build", "play": "jiti playground/index.ts", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && vitest run --coverage"}, "dependencies": {"destr": "^2.0.3", "node-fetch-native": "^1.6.4", "ufo": "^1.5.4"}, "devDependencies": {"@types/node": "^22.7.5", "@vitest/coverage-v8": "^2.1.2", "changelogen": "^0.5.7", "eslint": "^9.12.0", "eslint-config-unjs": "^0.4.1", "fetch-blob": "^4.0.0", "formdata-polyfill": "^4.0.10", "h3": "^1.13.0", "jiti": "^2.3.3", "listhen": "^1.9.0", "prettier": "^3.3.3", "std-env": "^3.7.0", "typescript": "^5.6.2", "unbuild": "^2.0.0", "undici": "^6.19.8", "vitest": "^2.1.2"}, "packageManager": "pnpm@9.12.1"}