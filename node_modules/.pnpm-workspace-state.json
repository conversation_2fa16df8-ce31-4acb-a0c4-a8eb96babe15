{"lastValidatedTimestamp": 1750689897780, "projects": {"/Users/<USER>/codes/work/webshell-monorepo": {"name": "webshell-monorepo", "version": "1.0.0"}, "/Users/<USER>/codes/work/webshell-monorepo/packages/core": {"name": "@webshell/core", "version": "1.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*"]}, "filteredInstall": true}