hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@rollup/rollup-darwin-arm64@4.44.0':
    '@rollup/rollup-darwin-arm64': private
  '@types/chai@5.2.2':
    '@types/chai': private
  '@types/deep-eql@4.0.2':
    '@types/deep-eql': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@vitest/expect@3.2.4':
    '@vitest/expect': private
  '@vitest/mocker@3.2.4(vite@6.3.5)':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.2.4':
    '@vitest/pretty-format': private
  '@vitest/runner@3.2.4':
    '@vitest/runner': private
  '@vitest/snapshot@3.2.4':
    '@vitest/snapshot': private
  '@vitest/spy@3.2.4':
    '@vitest/spy': private
  '@vitest/utils@3.2.4':
    '@vitest/utils': private
  '@xterm/xterm@5.5.0':
    '@xterm/xterm': private
  assertion-error@2.0.1:
    assertion-error: private
  cac@6.7.14:
    cac: private
  chai@5.2.0:
    chai: private
  check-error@2.1.1:
    check-error: private
  debug@4.4.1:
    debug: private
  deep-eql@5.0.2:
    deep-eql: private
  destr@2.0.5:
    destr: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  esbuild@0.25.5:
    esbuild: private
  estree-walker@3.0.3:
    estree-walker: private
  eventemitter3@5.0.1:
    eventemitter3: private
  expect-type@1.2.1:
    expect-type: private
  expiry-map@2.0.0:
    expiry-map: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fsevents@2.3.3:
    fsevents: private
  go-go-try@6.2.0:
    go-go-try: private
  js-tokens@9.0.1:
    js-tokens: private
  loupe@3.1.4:
    loupe: private
  magic-string@0.30.17:
    magic-string: private
  map-age-cleaner@0.2.0:
    map-age-cleaner: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  p-defer@1.0.0:
    p-defer: private
  packages/core:
    '@webshell/core': private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.0:
    pathval: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  rollup@4.44.0:
    rollup: private
  siginfo@2.0.0:
    siginfo: private
  source-map-js@1.2.1:
    source-map-js: private
  stackback@0.0.2:
    stackback: private
  std-env@3.9.0:
    std-env: private
  strip-literal@3.0.0:
    strip-literal: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinypool@1.1.1:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@4.0.3:
    tinyspy: private
  turbo-darwin-64@2.5.4:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.4:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.4:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.4:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.4:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.4:
    turbo-windows-arm64: private
  vite-node@3.2.4:
    vite-node: private
  vite@6.3.5:
    vite: private
  vitest@3.2.4:
    vitest: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.2
pendingBuilds: []
prunedAt: Fri, 20 Jun 2025 08:49:18 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - '@rollup/rollup-win32-x64-msvc@4.44.0'
  - turbo-darwin-64@2.5.4
  - turbo-linux-64@2.5.4
  - turbo-linux-arm64@2.5.4
  - turbo-windows-64@2.5.4
  - turbo-windows-arm64@2.5.4
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
