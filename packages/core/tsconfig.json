{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"composite": true, "strict": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "esModuleInterop": true, "skipLibCheck": true, "allowJs": true, "checkJs": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "module": "preserve", "moduleResolution": "bundler", "lib": ["ES2023", "DOM", "DOM.Iterable"]}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}