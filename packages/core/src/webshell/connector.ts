import ExpiryMap from 'expiry-map'
import EventEmitter from 'eventemitter3'

import type { Cache } from './types'
import { parseJsonrpcMessage } from '../utils/json-rpc'

export interface WebshellConnectorInitOptions {
  /**
   * Cache store for RPC request promises. Default to using `expiry-map`.
   *
   * The implementation must have the following methods: `get(key)`, `has(key)`, `set(key, val)`, `delete(key)`, `clear()`.
   *
   * The implementation must be able to store functions
   *
   * @default new ExpiryMap(15_000)
   */
  cache?: Cache<string, PromiseActions>

  /**
   * A `eventemitter3` instance.
   */
  eventEmitter?: EventEmitter

  /**
   * RPC request timeout in milliseconds.
   *
   * Set to negative to disable timeout.
   *
   * @default 10_000
   */
  requestTimeout?: number
}

interface PromiseActions<TValue = unknown> {
  resolve(value: TValue): void
  reject(reason?: any): void
}

export class WebshellConnector extends WebSocket {
  private promiseStore: Cache<string, PromiseActions>
  private eventEmitter: EventEmitter
  private requestTimeout: number

  constructor(url: string | URL, protocols?: string | string[], options?: WebshellConnectorInitOptions) {
    super(url, protocols)

    this.promiseStore = options?.cache ?? new ExpiryMap(10_000)
    this.eventEmitter = options?.eventEmitter ?? new EventEmitter()
    this.requestTimeout = options?.requestTimeout ?? 10_000

    this.addEventListener('message', (event: MessageEvent<string>) => {
      const response = parseJsonrpcMessage(event.data)

      if (!response.id && response.method) {
        this.eventEmitter.emit(response.method, response.params)
        return
      }

      const promise = this.promiseStore.get(response)

      if (promise) {
        this.promiseStore.delete(response.id)
        if (response.error) {
          promise.reject(new Error(response.error.message))
        } else {
          promise.resolve(response.result)
        }
      } else {
        this.eventEmitter.emit()
      }
    })
  }
}
